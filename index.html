<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web3 Coffee Shop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {}
        }
      }
    </script>
    <link rel="stylesheet" href="./styles.css">
  </head>

  <body class="bg-white">
    <div class="container max-w-4xl mx-auto p-6">
      <header class="text-center mb-10">
        <h1 class="text-4xl font-bold text-black mb-2">Web3 Coffee Shop</h1>
        <p class="text-lg text-black">Buy coffee with Ethereum</p>
      </header>

      <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="flex justify-end mb-4">
          <button id="connectButton" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-300">Connect Wallet</button>
        </div>

        <div class="grid md:grid-cols-2 gap-6">
          <div class="bg-gray-100 p-6 rounded-lg">
            <h2 class="text-2xl font-semibold text-black mb-4">Buy a Coffee</h2>
            <div class="mb-4">
              <label for="ethAmount" class="block text-black font-medium mb-2">ETH Amount</label>
              <input id="ethAmount" placeholder="0.1" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none" />
            </div>
            <button type="button" id="fundButton" class="coffee-btn w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Buy Coffee
            </button>
          </div>

          <div class="bg-gray-100 p-6 rounded-lg">
            <h2 class="text-2xl font-semibold text-black mb-4">Shop Management</h2>
            <div class="space-y-4">
              <button id="balanceButton" class="w-full bg-blue-400 hover:bg-blue-500 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-300 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Check Balance
              </button>
              <button id="withdrawButton" class="w-full bg-blue-400 hover:bg-blue-500 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-300 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                Withdraw Funds
              </button>
            </div>
          </div>
        </div>
      </div>

      <footer class="text-center text-gray-700 mt-8">
        <p>© 2025 Web3 Coffee Shop - Powered by Ethereum</p>
      </footer>
    </div>

    <!-- If you want to use the typescript or the JS, adjust this line. -->
    <!-- If you use the typescript, you'll need to run `pnpm dev` instead of running the raw HTML -->
    <!-- <script src="./index-ts.ts" type="module"></script> -->
    <script src="./index-js.js" type="module"></script>
  </body>
</html>
