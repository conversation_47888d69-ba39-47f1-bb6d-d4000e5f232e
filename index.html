<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web3 Coffee Shop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: {
              dark: {
                bg: '#0f172a',
                card: '#1e293b',
                text: '#f1f5f9'
              }
            }
          }
        }
      }
    </script>
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="./styles.css">
  </head>

  <body class="bg-white dark:bg-dark-bg transition-colors duration-300 min-h-screen">
    <div class="container max-w-4xl mx-auto p-6">
      <header class="text-center mb-10 relative">
        <!-- Theme Toggle Button -->
        <div class="absolute top-0 right-0">
          <button id="themeToggle" class="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
            <i data-lucide="sun" class="w-5 h-5 text-gray-800 dark:text-gray-200 dark:hidden"></i>
            <i data-lucide="moon" class="w-5 h-5 text-gray-200 hidden dark:block"></i>
          </button>
        </div>

        <div class="flex items-center justify-center mb-4">
          <i data-lucide="coffee" class="w-12 h-12 text-amber-600 dark:text-amber-400 mr-3"></i>
          <h1 class="text-4xl font-bold text-gray-900 dark:text-dark-text">Web3 Coffee Shop</h1>
        </div>
        <p class="text-lg text-gray-700 dark:text-gray-300">Buy coffee with Ethereum</p>
      </header>

      <div class="bg-white dark:bg-dark-card rounded-xl shadow-lg dark:shadow-2xl p-6 mb-8 border border-gray-200 dark:border-gray-700">
        <div class="flex justify-end mb-4">
          <button id="connectButton" class="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 flex items-center gap-2 shadow-md hover:shadow-lg">
            <i data-lucide="wallet" class="w-4 h-4"></i>
            Connect Wallet
          </button>
        </div>

        <div class="grid md:grid-cols-2 gap-6">
          <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
            <div class="flex items-center mb-4">
              <i data-lucide="shopping-cart" class="w-6 h-6 text-amber-600 dark:text-amber-400 mr-2"></i>
              <h2 class="text-2xl font-semibold text-gray-900 dark:text-dark-text">Buy a Coffee</h2>
            </div>
            <div class="mb-4">
              <label for="ethAmount" class="block text-gray-700 dark:text-gray-300 font-medium mb-2 flex items-center gap-2">
                <i data-lucide="coins" class="w-4 h-4"></i>
                ETH Amount
              </label>
              <input id="ethAmount" placeholder="0.1" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400" />
            </div>
            <button type="button" id="fundButton" class="coffee-btn w-full bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 dark:from-amber-600 dark:to-orange-600 dark:hover:from-amber-700 dark:hover:to-orange-700 text-white font-bold py-3 px-4 rounded-lg flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-300">
              <i data-lucide="coffee" class="w-5 h-5"></i>
              Buy Coffee
            </button>
          </div>

          <div class="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
            <div class="flex items-center mb-4">
              <i data-lucide="settings" class="w-6 h-6 text-blue-600 dark:text-blue-400 mr-2"></i>
              <h2 class="text-2xl font-semibold text-gray-900 dark:text-dark-text">Shop Management</h2>
            </div>
            <div class="space-y-4">
              <button id="balanceButton" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 dark:from-blue-600 dark:to-blue-700 dark:hover:from-blue-700 dark:hover:to-blue-800 text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-md hover:shadow-lg">
                <i data-lucide="wallet-2" class="w-5 h-5"></i>
                Check Balance
              </button>
              <button id="withdrawButton" class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 dark:from-green-600 dark:to-green-700 dark:hover:from-green-700 dark:hover:to-green-800 text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-md hover:shadow-lg">
                <i data-lucide="arrow-up-right" class="w-5 h-5"></i>
                Withdraw Funds
              </button>
            </div>
          </div>
        </div>
      </div>

      <footer class="text-center text-gray-600 dark:text-gray-400 mt-8">
        <div class="flex items-center justify-center gap-2 mb-2">
          <i data-lucide="zap" class="w-4 h-4 text-yellow-500"></i>
          <p>© 2025 Web3 Coffee Shop - Powered by Ethereum</p>
          <i data-lucide="zap" class="w-4 h-4 text-yellow-500"></i>
        </div>
      </footer>
    </div>

    <!-- Theme Toggle Script -->
    <script>
      // Initialize Lucide icons
      lucide.createIcons();

      // Theme toggle functionality
      const themeToggle = document.getElementById('themeToggle');
      const html = document.documentElement;

      // Check for saved theme preference or default to 'dark'
      const currentTheme = localStorage.getItem('theme') || 'dark';
      html.classList.toggle('dark', currentTheme === 'dark');

      themeToggle.addEventListener('click', () => {
        const isDark = html.classList.contains('dark');
        html.classList.toggle('dark', !isDark);
        localStorage.setItem('theme', !isDark ? 'dark' : 'light');
      });
    </script>

    <!-- If you want to use the typescript or the JS, adjust this line. -->
    <!-- If you use the typescript, you'll need to run `pnpm dev` instead of running the raw HTML -->
    <!-- <script src="./index-ts.ts" type="module"></script> -->
    <script src="./index-js.js" type="module"></script>
  </body>
</html>
