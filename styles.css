/* Custom styles */
body {
  min-height: 100vh;
  margin: 0;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  transition: background 0.3s ease, color 0.3s ease;
}

/* Dark mode background gradient */
html.dark body {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
  background-attachment: fixed;
}

/* Light mode background gradient */
html:not(.dark) body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  background-attachment: fixed;
}

.coffee-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.coffee-btn:hover {
  transform: translateY(-2px);
}

.coffee-btn:active {
  transform: translateY(0);
}

/* Add a subtle glow effect for dark mode buttons */
.dark .coffee-btn:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Light mode button hover effect */
body:not(.dark) .coffee-btn:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 0 20px rgba(245, 158, 11, 0.2);
}

.container {
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
  padding: 1.5rem;
}

/* Custom scrollbar for dark mode */
.dark ::-webkit-scrollbar {
  width: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  .container {
    padding: 1rem;
  }

  /* Ensure buttons are touch-friendly */
  button {
    min-height: 44px;
    touch-action: manipulation;
  }

  /* Improve input field usability on mobile */
  input[type="text"], input[type="number"], input {
    font-size: 16px; /* Prevents zoom on iOS */
    -webkit-appearance: none;
    border-radius: 8px;
  }

  /* Better spacing for mobile cards */
  .coffee-btn {
    padding: 1rem;
    font-size: 1rem;
  }
}

/* Tablet and small desktop improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .container {
    padding: 1.5rem;
  }
}
