/* Tailwind CSS Reset and Base Styles */
*, ::before, ::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
}

body {
  margin: 0;
  line-height: inherit;
  min-height: 100vh;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: background 0.3s ease, color 0.3s ease;
}

/* Reset for common elements */
h1, h2, h3, h4, h5, h6 { margin: 0; font-size: inherit; font-weight: inherit; }
p { margin: 0; }
button { background-color: transparent; background-image: none; padding: 0; }
input { margin: 0; padding: 0; }
svg { display: block; vertical-align: middle; }

/* Essential Tailwind Utilities */
.min-h-screen { min-height: 100vh; }
.max-w-4xl { max-width: 56rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-full { width: 100%; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.flex-1 { flex: 1 1 0%; }
.flex-shrink-0 { flex-shrink: 0; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.space-y-3 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.75rem; }
.space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.border { border-width: 1px; }
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1f2937; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }
.border-gray-600 { border-color: #4b5563; }
.border-gray-700 { border-color: #374151; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }
.text-white { color: #ffffff !important; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }
.text-amber-600 { color: #d97706; }
.text-blue-600 { color: #2563eb; }
.text-yellow-500 { color: #eab308; }

/* Ensure all button text is visible */
button {
  color: inherit;
}

/* Specific button text color fixes */
.bg-blue-500,
.bg-blue-600,
.hover\:bg-blue-600:hover,
.dark\:bg-blue-600,
.dark\:hover\:bg-blue-700:hover {
  color: #ffffff !important;
}

/* Gradient button text visibility */
.bg-gradient-to-r {
  color: #ffffff !important;
}

.bg-gradient-to-r * {
  color: #ffffff !important;
}
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.transition-colors { transition-property: color, background-color, border-color; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.outline-none { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:border-transparent:focus { border-color: transparent; }

/* Dark mode background gradient */
html.dark body {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
  background-attachment: fixed;
}

/* Light mode background gradient */
html:not(.dark) body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  background-attachment: fixed;
}

/* Dark mode utilities */
.dark .dark\:bg-dark-card { background-color: #1e293b; }
.dark .dark\:bg-gray-700 { background-color: #374151; }
.dark .dark\:bg-gray-800 { background-color: #1f2937; }
.dark .dark\:border-gray-600 { border-color: #4b5563; }
.dark .dark\:border-gray-700 { border-color: #374151; }
.dark .dark\:text-white { color: #ffffff; }
.dark .dark\:text-gray-100 { color: #f3f4f6; }
.dark .dark\:text-gray-200 { color: #e5e7eb; }
.dark .dark\:text-gray-300 { color: #d1d5db; }
.dark .dark\:text-gray-400 { color: #9ca3af; }
.dark .dark\:text-amber-400 { color: #fbbf24; }
.dark .dark\:text-blue-400 { color: #60a5fa; }
.dark .dark\:shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
.dark .dark\:hover\:bg-gray-600:hover { background-color: #4b5563; }
.dark .dark\:hidden { display: none; }
.dark .hidden.dark\:block { display: block; }

/* Enhanced Button gradients with hover effects */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
  position: relative;
  overflow: hidden;
}

.bg-gradient-to-r::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.bg-gradient-to-r:hover::after {
  transform: translateX(100%);
}

.from-amber-500 { --tw-gradient-from: #f59e0b; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(245, 158, 11, 0)); }
.to-orange-500 { --tw-gradient-to: #f97316; }
.from-blue-500 { --tw-gradient-from: #3b82f6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0)); }
.to-blue-600 { --tw-gradient-to: #2563eb; }
.from-green-500 { --tw-gradient-from: #10b981; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(16, 185, 129, 0)); }
.to-green-600 { --tw-gradient-to: #059669; }

/* Enhanced hover gradient transitions */
.hover\:from-amber-600:hover {
  --tw-gradient-from: #d97706;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 35px rgba(217, 119, 6, 0.3);
}
.hover\:to-orange-600:hover {
  --tw-gradient-to: #ea580c;
}
.hover\:from-blue-600:hover {
  --tw-gradient-from: #2563eb;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 35px rgba(37, 99, 235, 0.3);
}
.hover\:to-blue-700:hover {
  --tw-gradient-to: #1d4ed8;
}
.hover\:from-green-600:hover {
  --tw-gradient-from: #059669;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 15px 35px rgba(5, 150, 105, 0.3);
}
.hover\:to-green-700:hover {
  --tw-gradient-to: #047857;
}

/* Enhanced Theme Toggle Button */
#themeToggle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

#themeToggle:hover {
  transform: rotate(15deg) scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dark #themeToggle:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.2);
}

#themeToggle:active {
  transform: rotate(0deg) scale(0.95);
  transition: all 0.1s ease;
}

/* Icon rotation animation */
#themeToggle i {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#themeToggle:hover i {
  transform: rotate(180deg);
}

/* Enhanced Connect Wallet Button */
#connectButton {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  color: #ffffff !important; /* Ensure text is always white */
}

#connectButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

#connectButton:hover::before {
  left: 100%;
}

#connectButton:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.3);
  color: #ffffff !important; /* Ensure text stays white on hover */
}

.dark #connectButton:hover {
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.4), 0 0 25px rgba(59, 130, 246, 0.2);
}

#connectButton:active {
  transform: translateY(0) scale(0.98);
  transition: all 0.1s ease;
  color: #ffffff !important; /* Ensure text stays white when active */
}

/* Ensure Connect Wallet button text and icon are always visible */
#connectButton,
#connectButton:hover,
#connectButton:focus,
#connectButton:active {
  color: #ffffff !important;
}

#connectButton i {
  color: #ffffff !important;
}

/* Comprehensive button text visibility fixes */
/* Blue buttons (Connect Wallet) */
button[id*="connect"],
button.bg-blue-500,
button.bg-blue-600,
.bg-blue-500,
.bg-blue-600 {
  color: #ffffff !important;
}

/* All gradient buttons */
button.bg-gradient-to-r,
.bg-gradient-to-r,
#fundButton,
#balanceButton,
#withdrawButton {
  color: #ffffff !important;
}

/* Ensure button text is never transparent or invisible */
button,
.coffee-btn,
#connectButton,
#fundButton,
#balanceButton,
#withdrawButton {
  color: #ffffff !important;
}

/* Button icons should also be white */
button i,
.coffee-btn i,
#connectButton i,
#fundButton i,
#balanceButton i,
#withdrawButton i {
  color: #ffffff !important;
}

/* Override any conflicting styles */
button *,
.coffee-btn *,
#connectButton *,
#fundButton *,
#balanceButton *,
#withdrawButton * {
  color: inherit !important;
}

/* Enhanced Input Field Hover Effects */
input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

input:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark input:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

input:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.dark input:focus {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Enhanced Card Hover Effects */
.bg-gray-50, .dark .dark\:bg-gray-800 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bg-gray-50:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
}

.dark .dark\:bg-gray-800:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
}

/* Icon Hover Effects */
i[data-lucide] {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover i[data-lucide] {
  transform: scale(1.2) rotate(5deg);
}

#themeToggle:hover i[data-lucide] {
  transform: scale(1.1) rotate(180deg);
}

/* Pulse animation for important buttons */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.coffee-btn:hover {
  animation: pulse 2s infinite;
}

/* Hover effects */
.hover\:bg-gray-300:hover { background-color: #d1d5db; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Enhanced Button Hover Effects */
.coffee-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.coffee-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.coffee-btn:hover::before {
  left: 100%;
}

.coffee-btn:hover {
  transform: translateY(-3px) scale(1.02);
}

.coffee-btn:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}

/* Enhanced glow effects for dark mode buttons */
.dark .coffee-btn:hover {
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.4),
    0 5px 15px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(59, 130, 246, 0.4);
}

/* Enhanced light mode button hover effect */
body:not(.dark) .coffee-btn:hover {
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.15),
    0 5px 15px rgba(0, 0, 0, 0.1),
    0 0 25px rgba(245, 158, 11, 0.3);
}

.container {
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
  padding: 1.5rem;
}

/* Custom scrollbar for dark mode */
.dark ::-webkit-scrollbar {
  width: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Enhanced smooth transitions for theme switching */
* {
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Floating animation for cards */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.bg-white, .dark .dark\:bg-dark-card {
  animation: float 6s ease-in-out infinite;
}

/* Subtle glow animation for theme toggle */
@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.6); }
}

.dark #themeToggle {
  animation: glow 3s ease-in-out infinite;
}

/* Ripple effect for buttons */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.coffee-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.coffee-btn:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

/* Cursor Styles for Interactive Elements */
button,
.coffee-btn,
#themeToggle,
#connectButton,
#fundButton,
#balanceButton,
#withdrawButton,
input[type="button"],
input[type="submit"],
[role="button"],
.cursor-pointer {
  cursor: pointer;
}

/* Hover cursor for interactive cards */
.bg-gray-50:hover,
.dark .dark\:bg-gray-800:hover {
  cursor: pointer;
}

/* Text cursor for input fields */
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
textarea,
[contenteditable] {
  cursor: text;
}

/* Disabled state cursor */
button:disabled,
input:disabled,
.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Loading state cursor */
.loading {
  cursor: wait;
}

/* Help cursor for elements with tooltips */
[title],
.tooltip {
  cursor: help;
}

/* Move cursor for draggable elements */
[draggable="true"] {
  cursor: move;
}

/* Zoom cursor for zoomable images */
img:hover {
  cursor: zoom-in;
}

/* Enhanced cursor interactions */
/* Pointer cursor for icons inside buttons */
button i,
.coffee-btn i,
#themeToggle i {
  cursor: pointer;
  pointer-events: none; /* Prevent icon from blocking button clicks */
}

/* Pointer cursor for labels that are clickable */
label[for] {
  cursor: pointer;
}

/* Custom cursor for specific button types */
#fundButton {
  cursor: pointer;
}

#fundButton:hover {
  cursor: pointer;
}

#balanceButton:hover,
#withdrawButton:hover {
  cursor: pointer;
}

/* Ensure all interactive elements show pointer */
a,
a:hover,
button:hover,
.coffee-btn:hover,
#themeToggle:hover,
input[type="button"]:hover,
input[type="submit"]:hover {
  cursor: pointer;
}

/* Special cursor for theme toggle to indicate it's interactive */
#themeToggle {
  cursor: pointer;
}

#themeToggle:hover {
  cursor: pointer;
}

/* Make sure focus states also show pointer */
button:focus,
.coffee-btn:focus {
  cursor: pointer;
}

/* Prevent text selection on buttons for better UX */
button,
.coffee-btn,
#themeToggle,
#connectButton,
#fundButton,
#balanceButton,
#withdrawButton {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Responsive utilities */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }

@media (min-width: 640px) {
  .sm\:p-6 { padding: 1.5rem; }
  .sm\:mb-6 { margin-bottom: 1.5rem; }
  .sm\:mb-10 { margin-bottom: 2.5rem; }
  .sm\:w-12 { width: 3rem; }
  .sm\:h-12 { height: 3rem; }
  .sm\:w-auto { width: auto; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:items-center { align-items: center; }
  .sm\:justify-center { justify-content: center; }
  .sm\:justify-end { justify-content: flex-end; }
  .sm\:justify-start { justify-content: flex-start; }
  .sm\:gap-6 { gap: 1.5rem; }
  .sm\:space-y-4 > :not([hidden]) ~ :not([hidden]) { margin-top: 1rem; }
  .sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .sm\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .sm\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
  .sm\:text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
  .sm\:py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
  .sm\:py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
  .sm\:px-4 { padding-left: 1rem; padding-right: 1rem; }
  .sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  .container {
    padding: 1rem;
  }

  .flex-col { flex-direction: column; }
  .py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
  .py-4 { padding-top: 1rem; padding-bottom: 1rem; }
  .px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

  /* Ensure buttons are touch-friendly */
  button {
    min-height: 44px;
    touch-action: manipulation;
  }

  /* Improve input field usability on mobile */
  input[type="text"], input[type="number"], input {
    font-size: 16px; /* Prevents zoom on iOS */
    -webkit-appearance: none;
    border-radius: 8px;
  }

  /* Better spacing for mobile cards */
  .coffee-btn {
    padding: 1rem;
    font-size: 1rem;
  }
}

/* Tablet and small desktop improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .container {
    padding: 1.5rem;
  }
}
