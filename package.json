{"name": "html-js-coffee-cu", "type": "module", "scripts": {"dev": "vite", "anvil": "anvil --load-state fundme-anvil.json", "format": "prettier --write .", "compile": "tsc --project tsconfig.json ", "build": "npm run build:css && vite build", "build:css": "tailwindcss -i ./src/input.css -o ./dist/output.css --watch=false", "build:css:watch": "tailwindcss -i ./src/input.css -o ./dist/output.css --watch", "preview": "vite preview"}, "dependencies": {"viem": "^2.23.2"}, "devDependencies": {"autoprefixer": "^10.4.20", "postcss": "^8.5.0", "prettier": "^3.5.0", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "vite": "^6.1.0"}}